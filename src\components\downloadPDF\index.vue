<template>
  <div class="downloadPDF"></div>
  <view :variation :change:variation="renderScript.watchVariationChange" />
</template>

<script>
export default {
  emits: ['markerClick', 'mapClick', 'mapLoad'],
  props: { variation: { type: Object, default: null } },
  data() {
    return {}
  },
  methods: {
    downPdf(path) {
      var fileName = new Date().valueOf() + '.pdf'
      let that = this
      this.base64ToFile(path, fileName, function (path1) {
        uni.showToast({
          title: `已保存在文件夹下！位置为：/storage/emulated/0/PDF存放处/${fileName}`,
          icon: 'none',
          duration: 2000,
          position: 'top'
        })
        setTimeout(() => {
          //自行控制是否打开文件

          //用第三方程序打开文件
          plus.runtime.openFile(`/storage/emulated/0/PDF存放处/${fileName}`, {}, function (error) {
            plus.nativeUI.toast(error.message)
          })
        }, 2000)
      })
    },
    base64ToFile(base64Str, fileName, callback) {
      //申请本地存储读写权限，创建文件夹
      plus.android.requestPermissions(
        ['android.permission.WRITE_EXTERNAL_STORAGE', 'android.permission.READ_EXTERNAL_STORAGE', 'android.permission.INTERNET', 'android.permission.ACCESS_WIFI_STATE'],
        (error) => {
          const File = plus.android.importClass('java.io.File')
          let file = new File('/storage/emulated/0/PDF存放处')
          if (!file.exists()) {
            //文件夹不存在即创建
            return file.mkdirs()
          }
          return false
        },
        (success) => {
          uni.showToast({
            title: '无法获取权限，文件下载将出错！',
            icon: 'none'
          })
        }
      )
      // 去除base64前缀,进行文件保存
      var index = base64Str.indexOf(',')
      var base64Data = base64Str.slice(index + 1, base64Str.length)
      let that = this
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function (fs) {
        fs.root.getFile(
          fileName,
          {
            create: true
          },
          function (entry) {
            // 获得本地路径URL，file:///xxx/doc/1663062980631.xlsx
            var fullPath = '/storage/emulated/0/PDF存放处/' + fileName
            var FileOutputStream = plus.android.importClass('java.io.FileOutputStream')
            var out = new FileOutputStream(fullPath)

            try {
              // 使用分块处理避免Base64.decode长度限制和内存问题
              that.writeBase64InChunks(base64Data, out, () => {
                out.close()
                // 回调
                callback && callback()
              })
            } catch (error) {
              console.error('文件写入失败:', error)
              out.close()
              uni.showToast({
                title: '文件保存失败！',
                icon: 'none'
              })
            }
          }
        )
      })
    },

    // 分块写入base64数据，避免内存溢出和长度限制
    writeBase64InChunks(base64Data, outputStream, callback) {
      const CHUNK_SIZE = 8192 // 8KB chunks，可根据设备性能调整
      let offset = 0
      let hasError = false

      const writeNextChunk = () => {
        if (hasError) return

        if (offset >= base64Data.length) {
          callback()
          return
        }

        try {
          const remainingLength = base64Data.length - offset
          const chunkSize = Math.min(CHUNK_SIZE, remainingLength)
          let chunk = base64Data.slice(offset, offset + chunkSize)

          // 确保chunk长度是4的倍数（base64特性）
          // 如果不是最后一块且长度不是4的倍数，调整chunk大小
          if (offset + chunkSize < base64Data.length && chunk.length % 4 !== 0) {
            const adjustment = 4 - (chunk.length % 4)
            chunk = base64Data.slice(offset, offset + chunkSize + adjustment)
          }

          const bytes = this.base64ChunkToByteArray(chunk)
          if (bytes.length > 0) {
            outputStream.write(bytes)
            offset += chunk.length

            // 使用setTimeout避免阻塞UI线程，提供更好的用户体验
            setTimeout(writeNextChunk, 0)
          } else {
            hasError = true
            console.error('Base64解码失败，chunk为空')
          }
        } catch (error) {
          hasError = true
          console.error('分块写入失败:', error)
        }
      }

      writeNextChunk()
    },

    // 优化的分块base64转字节数组方法
    base64ChunkToByteArray(chunk) {
      try {
        // 处理padding，确保是有效的base64字符串
        while (chunk.length % 4 !== 0) {
          chunk += '='
        }

        const binaryString = atob(chunk)
        const bytes = []

        for (let i = 0; i < binaryString.length; i++) {
          const byte = binaryString.charCodeAt(i)
          // 转换为有符号字节（Java byte范围：-128到127）
          bytes.push(byte >= 128 ? byte - 256 : byte)
        }

        return bytes
      } catch (error) {
        console.error('Base64解码失败:', error, 'chunk length:', chunk.length)
        return []
      }
    },

    // 保留原方法作为备用方案（适用于小文件）
    base64ToByteArray(base64Str) {
      try {
        const binaryString = atob(base64Str)
        const uint8Array = new Uint8Array(binaryString.length)

        for (let i = 0; i < binaryString.length; i++) {
          uint8Array[i] = binaryString.charCodeAt(i)
        }
        let arr = []
        Array.from(uint8Array).map((num) => {
          arr.push(num >= 128 ? num - 256 : num)
        })
        return arr
      } catch (error) {
        console.error('传统base64转换失败:', error)
        return []
      }
    },
    showLoading() {
      uni.showLoading({ title: '下载中...' })
    },
    hideLoading() {
      uni.hideLoading()
    }
  }
}
</script>

<script lang="renderjs" module="renderScript">
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
export default {
  methods: {
    watchVariationChange({type, value}) {
      if(type && value) this[type](value)
    },
    // 页面导出pdf
    download(id){
			this.$ownerInstance.callMethod('showLoading'); //显示加载
      const detail = document.querySelector(id);
			html2Canvas(detail, {
				allowTaint: true,
				useCORS: true,
				scale: 2, // 提高分辨率
				windowHeight: detail.scrollHeight // 确保捕获完整高度
			}).then((canvas) => {
				return new Promise((resolve) => {
					setTimeout(() => resolve(canvas), 500)
				}).then((canvas) => {
					const contentWidth = canvas.width;
					const contentHeight = canvas.height;

					// 创建自定义尺寸的PDF（核心修改）
					const pdf = new JsPDF({
						orientation: contentWidth > contentHeight ? 'l' : 'p', // 自动方向
						unit: 'px',
						format: [contentWidth, contentHeight] // 完全匹配内容尺寸
					});

					// 直接添加完整内容（移除分页逻辑）
					pdf.addImage(canvas, 'PNG', 0, 0, contentWidth, contentHeight);

					const blob = pdf.output("datauristring");
					this.$ownerInstance.callMethod('downPdf', blob);
				}).catch((r) => {
					console.log(r);
					this.$ownerInstance.callMethod('hideLoading');
				})
			});
    }
  },
}
</script>
