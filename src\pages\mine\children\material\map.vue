<template>
  <div class="all flex f-column">
    <wd-search @search="debounceHandlesearchChange" @change="debounceHandlesearchChange" />
    <div class="f-1"><MapGlS :variation @mapClick="mapClick" @mapLoad="mapLoad" /></div>

    <div class="absolute box-shadow f-xy-center pad-10 back-white border-R12 z-index-9" @click="toListClick" style="bottom: 200rpx; right: 50rpx">
      <wd-icon name="view-list" color="#2d2d2d" size="24px"></wd-icon>
    </div>
  </div>

  <wd-floating-panel custom-class="floating-panel_c relative" v-model:height="height" :anchors="anchors">
    <div class="pad-X16"><PopupDetali :detail /></div>
  </wd-floating-panel>

  <!-- 搜索列表弹窗 -->
  <wd-popup :modal="false" v-model="searchOpen" closable position="bottom" custom-style="min-height: 1000rpx; z-index:99">
    <div class="pad-24">
      <div class="pad-Y32"></div>
      <div class="flex f-between" style="background-color: #32cbdb">
        <div class="f-1 pad-16 text-center color-white">小区名称</div>
        <div class="f-1 pad-16 text-center color-white">小区编码</div>
      </div>
      <div class="overflow-auto" style="max-height: 800rpx">
        <template v-for="(item, index) in searchList" :key="index">
          <div class="item pad-16 mar-Y12 border-B-eee flex f-between" @click="searchItemClick(item)">
            <div class="f-1 text-center">{{ item.name }}</div>
            <div class="f-1 text-center">{{ item.Zone_Code }}</div>
          </div>
        </template>
        <wd-status-tip v-if="searchList.length == 0" image="search" tip="当前搜索无结果" />
      </div>
    </div>
  </wd-popup>
  <FeedbackPopup ref="feedbackRef" :id="FeedbackID" />

  <wd-action-sheet v-model="actionSheetOpen" :actions @select="selectActionSheet" />

  <FloatingButton :visible="height !== 0" :draggable="true" :size="80" icon="move" background-color="#33aa71" :initial-position="{ x: 12, y: 650 }" :bounds="{ top: 100, right: 50, bottom: 100, left: 50 }" @click="handleMoveClick">
    <wd-icon name="chat" size="24px" color="#ffffff"></wd-icon>
  </FloatingButton>

  <wd-toast />
</template>

<script setup>
import { ref, watch } from 'vue'

import MapGlS from './components/MapGlS/index.vue'
import PopupDetali from './components/PopupDetali.vue'
import { onNavigationBarButtonTap, onLoad, onBackPress } from '@dcloudio/uni-app'
import debounce from 'lodash/debounce'
import { getZoneDataLike } from '/src/services/model/map.js'
import FeedbackPopup from './components/FeedbackPopup/index.vue'
import { feedbackIssueCreateRecordApi } from '/src/services/model/feedback.issue.js'
import { getSQL, getFuzzyDetail } from '/src/services/model/material'
import { getZoneData } from '/src/services/model/map.js'
import { useToast } from 'wot-design-uni'
import FloatingButton from '/src/components/FloatingButton/index.vue'

const variation = ref(null)
const height = ref(0)
const anchors = ref([])
const searchOpen = ref(false)
const toast = useToast()
const feedbackRef = ref(null)

// 监听按钮点击事件
onNavigationBarButtonTap(() => feedbackRef.value.getFeedbackDetail())
onBackPress(() => {
  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (height.value !== 0 || searchOpen.value === true || isShow) {
    searchOpen.value = false
    height.value = 0
    feedbackRef.value.close()
    return true
  }
})

onLoad(async () => {
  const windowHeight = uni.getSystemInfoSync().windowHeight
  anchors.value = [0, Math.round(1 * windowHeight)]
  height.value = anchors.value[0]
  mapLoad()
})

async function mapLoad() {
  toast.loading('加载中')
  try {
    const res = await getSQL('SELECT Zone_Code FROM [dbo].[DIST_ADDRESS]  where Client_Name is not NULL')
    const codes = JSON.parse(res.data).map((item) => item.Zone_Code)
    variation.value = { type: 'setFillColor', value: codes }
    toast.close()
  } catch (error) {
    toast.close()
    toast.error('获取数据失败')
  }
}

const detail = ref(null)
const actionSheetOpen = ref(false)
const actions = ref([])
async function mapClick({ Zone_Code }) {
  toast.loading('加载中...')
  try {
    const result = await getZoneData(Zone_Code)
    const [res] = JSON.parse(result.data)
    const codes = res.Client_Name.split(';')

    if (codes.length > 1) {
      const datas = (await Promise.all(codes.map((code) => getFuzzyDetail(code)))).map(([item]) => item)
      actions.value = datas.map((item) => ({ name: item.xqmc, value: item }))
      actionSheetOpen.value = true
      toast.close()
      return
    }

    const [data] = await getFuzzyDetail(res.Client_Name)
    detail.value = data

    //   处理进水路数数据
    if (detail.value.jsgl == '[]' || !detail.value.jsgl || detail.value.jsgl?.length < 10) {
      detail.value.jsgl = []
    } else {
      detail.value.jsgl = JSON.parse(detail.value.jsgl)
    }
    height.value = anchors.value[1]
    toast.close()
  } catch (error) {
    toast.close()
    toast.error('获取数据失败')
  }
}

function selectActionSheet({ item }) {
  detail.value = item.value

  //   处理进水路数数据
  if (detail.value.jsgl == '[]' || !detail.value.jsgl || detail.value.jsgl?.length < 10) {
    detail.value.jsgl = []
  } else {
    detail.value.jsgl = JSON.parse(detail.value.jsgl)
  }
  height.value = anchors.value[1]
}

// 搜索
const searchList = ref([])
const debounceHandlesearchChange = debounce(searchChange, 500)
async function searchChange({ value }) {
  if (!value) return
  const res = await getZoneDataLike(value)
  const result = JSON.parse(res.data)
  searchList.value = result.map((i) => ({ name: i.Zone_Name, Zone_Code: i.Zone_Code, Center_Point: i.Center_Point.split(',') }))
  searchOpen.value = true
}

// 搜索内容点击
function searchItemClick(item) {
  variation.value = { type: 'reach', value: item }
  searchOpen.value = false
}

function toListClick() {
  uni.navigateTo({ url: '/src/pages/mine/children/material/list' })
}

const FeedbackID = ref()
async function handleMoveClick() {
  const { data } = await feedbackIssueCreateRecordApi({ FeedbackContent: `${detail.value.xqmc}错误数据反馈`, FileCode: detail.value.xqbm })
  FeedbackID.value = data.FeedbackID

  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (isShow) {
    feedbackRef.value.close()
  } else {
    feedbackRef.value.getFeedbackDetail(data.FeedbackID)
  }
}
</script>

<style lang="less" scoped>
.floating-panel_c {
  background-color: rgba(255, 255, 255, 0.6);
  :deep(.wd-floating-panel__header) {
    height: 14px;
  }
  :deep(.wd-floating-panel__content) {
    background-color: transparent;
  }
}

:deep(.wd-tabs__map-nav-btn) {
  width: auto;
  height: auto;
  padding: 8rpx 18rpx;
  margin-bottom: 10rpx;
  line-height: normal;
}

.item {
  background-color: #eee;
}
.item:nth-child(2n) {
  background-color: antiquewhite;
}
</style>
